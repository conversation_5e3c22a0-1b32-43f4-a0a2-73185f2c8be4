#include "AuracronDynamicRail.h"
#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronDynamicRealmBridge.h"
#include "Components/SplineComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/AudioComponent.h"
#include "Components/LightComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "NiagaraSystem.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialInterface.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "MetasoundSource.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "GameplayTags.h"

/**
 * Auracron Dynamic Rail Implementation
 * 
 * Production-ready implementation using UE 5.6 latest APIs
 * Supports Solar, Axis, and Lunar rail types with advanced behaviors
 */

AAuracronDynamicRail::AAuracronDynamicRail()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.TickGroup = TG_PostPhysics;
    
    // Initialize components using UE 5.6 component system
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootScene"));
    RootComponent = RootSceneComponent;
    
    // Create spline component for rail path
    RailSpline = CreateDefaultSubobject<USplineComponent>(TEXT("RailSpline"));
    RailSpline->SetupAttachment(RootComponent);
    RailSpline->SetClosedLoop(false);
    RailSpline->SetSplinePointType(0, ESplinePointType::CurveClamped);
    
    // Create rail mesh component
    RailMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("RailMesh"));
    RailMesh->SetupAttachment(RootComponent);
    RailMesh->SetCollisionEnabled(ECollisionEnabled::NoCollision);
    RailMesh->SetCastShadow(true);
    
    // Create interaction trigger
    InteractionTrigger = CreateDefaultSubobject<UBoxComponent>(TEXT("InteractionTrigger"));
    InteractionTrigger->SetupAttachment(RootComponent);
    InteractionTrigger->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    InteractionTrigger->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
    InteractionTrigger->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
    InteractionTrigger->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Overlap);
    InteractionTrigger->SetBoxExtent(FVector(200.0f, 200.0f, 100.0f));
    
    // Create VFX component
    RailVFX = CreateDefaultSubobject<UNiagaraComponent>(TEXT("RailVFX"));
    RailVFX->SetupAttachment(RootComponent);
    RailVFX->SetAutoActivate(false);
    
    // Create audio component
    RailAudio = CreateDefaultSubobject<UAudioComponent>(TEXT("RailAudio"));
    RailAudio->SetupAttachment(RootComponent);
    RailAudio->SetAutoActivate(false);
    
    // Initialize default values
    RailType = EAuracronRailType::Solar;
    bIsActive = false;
    bAutoActivate = true;
    bIsInitialized = false;
    LastUpdateTime = 0.0f;
    LastVisibilityCheck = 0.0f;
    ActivePlayerCount = 0;
    
    // Initialize movement data
    MovementData.MovementSpeed = 1500.0f;
    MovementData.Acceleration = 1000.0f;
    MovementData.Deceleration = 1200.0f;
    MovementData.bCanReverseDirection = true;
    MovementData.bCanExitAnywhere = true;
    MovementData.EnergyCostPerSecond = 8.0f;
    
    // Initialize visual config
    VisualConfig.RailColor = FLinearColor(1.0f, 0.8f, 0.0f, 1.0f); // Golden default
    VisualConfig.EffectIntensity = 1.0f;
    VisualConfig.bVisibleDuringDay = true;
    VisualConfig.bVisibleDuringNight = false;
    
    // Setup overlap events
    InteractionTrigger->OnComponentBeginOverlap.AddDynamic(this, &AAuracronDynamicRail::OnTriggerBeginOverlap);
    InteractionTrigger->OnComponentEndOverlap.AddDynamic(this, &AAuracronDynamicRail::OnTriggerEndOverlap);
}

void AAuracronDynamicRail::BeginPlay()
{
    Super::BeginPlay();
    
    // Initialize rail based on type using UE 5.6 initialization system
    InitializeRailByType();
    
    // Cache realm subsystem reference
    if (UWorld* World = GetWorld())
    {
        CachedRealmSubsystem = World->GetSubsystem<UAuracronDynamicRealmSubsystem>();
    }
    
    // Auto-activate if configured
    if (bAutoActivate)
    {
        ActivateRail();
    }
    
    // Start update timers using UE 5.6 timer system
    GetWorld()->GetTimerManager().SetTimer(
        EffectUpdateTimer,
        [this]()
        {
            UpdateRailVisuals(GetWorld()->GetDeltaSeconds());
        },
        0.033f, // ~30 FPS for visual updates
        true    // Looping
    );
    
    GetWorld()->GetTimerManager().SetTimer(
        VisibilityUpdateTimer,
        [this]()
        {
            UpdateRailVisibility();
        },
        1.0f,   // Check visibility every second
        true    // Looping
    );
    
    bIsInitialized = true;
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Dynamic Rail initialized - Type: %s"), *UEnum::GetValueAsString(RailType));
}

void AAuracronDynamicRail::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Cleanup all players on rail
    for (APawn* Player : PlayersOnRail)
    {
        if (Player)
        {
            StopPlayerMovement(Player);
        }
    }
    
    // Clear timers
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(EffectUpdateTimer);
        World->GetTimerManager().ClearTimer(VisibilityUpdateTimer);
        World->GetTimerManager().ClearTimer(PlayerMovementTimer);
    }
    
    // Clear dynamic materials
    DynamicRailMaterials.Empty();
    
    Super::EndPlay(EndPlayReason);
}

void AAuracronDynamicRail::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (!bIsInitialized || !bIsActive)
    {
        return;
    }
    
    LastUpdateTime += DeltaTime;
    
    // Update rail type-specific behavior using UE 5.6 behavior system
    switch (RailType)
    {
        case EAuracronRailType::Solar:
            UpdateSolarRailBehavior(DeltaTime);
            break;
        case EAuracronRailType::Axis:
            UpdateAxisRailBehavior(DeltaTime);
            break;
        case EAuracronRailType::Lunar:
            UpdateLunarRailBehavior(DeltaTime);
            break;
        default:
            break;
    }
    
    // Update player movement
    for (APawn* Player : PlayersOnRail)
    {
        if (Player)
        {
            UpdatePlayerMovement(Player, DeltaTime);
        }
    }
    
    // Performance optimization every 5 seconds
    if (LastUpdateTime >= 5.0f)
    {
        OptimizeRailRendering(GetActorLocation());
        LastUpdateTime = 0.0f;
    }
}

void AAuracronDynamicRail::InitializeRailByType()
{
    // Initialize rail based on type using UE 5.6 type-specific initialization
    switch (RailType)
    {
        case EAuracronRailType::Solar:
            InitializeSolarRail();
            break;
        case EAuracronRailType::Axis:
            InitializeAxisRail();
            break;
        case EAuracronRailType::Lunar:
            InitializeLunarRail();
            break;
        default:
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Unknown rail type for initialization"));
            break;
    }
    
    // Build rail mesh and setup effects
    BuildRailMesh();
    SetupRailEffects();
    ConfigureRailAudio();
    CreateEntryExitPoints();
    SetupRailCollision();
}

void AAuracronDynamicRail::InitializeSolarRail()
{
    // Initialize Solar Rail with golden speed characteristics using UE 5.6 solar system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Solar Rail..."));
    
    // Configure movement data for solar rail
    MovementData.MovementSpeed = 1500.0f;
    MovementData.Acceleration = 1200.0f;
    MovementData.Deceleration = 1000.0f;
    MovementData.EnergyCostPerSecond = 8.0f;
    
    // Configure visual properties
    VisualConfig.RailColor = FLinearColor(1.0f, 0.8f, 0.0f, 1.0f); // Golden
    VisualConfig.EffectIntensity = 1.2f;
    VisualConfig.bVisibleDuringDay = true;
    VisualConfig.bVisibleDuringNight = false; // Reduced at night
    
    // Load solar-specific assets
    static const FSoftObjectPath SolarMeshPath(TEXT("/Game/Environment/Rails/SM_SolarRail.SM_SolarRail"));
    if (UStaticMesh* SolarMesh = Cast<UStaticMesh>(SolarMeshPath.TryLoad()))
    {
        RailMesh->SetStaticMesh(SolarMesh);
    }
    
    // Load solar VFX
    static const FSoftObjectPath SolarVFXPath(TEXT("/Game/VFX/Rails/NS_SolarRailEffect.NS_SolarRailEffect"));
    if (UNiagaraSystem* SolarVFX = Cast<UNiagaraSystem>(SolarVFXPath.TryLoad()))
    {
        RailVFX->SetAsset(SolarVFX);
        VisualConfig.RailEffect = SolarVFX;
    }
    
    // Load solar audio
    static const FSoftObjectPath SolarAudioPath(TEXT("/Game/Audio/Rails/MS_SolarRailAmbient.MS_SolarRailAmbient"));
    if (UMetaSoundSource* SolarAudio = Cast<UMetaSoundSource>(SolarAudioPath.TryLoad()))
    {
        RailAudio->SetSound(SolarAudio);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Solar Rail initialized with golden speed characteristics"));
}

void AAuracronDynamicRail::InitializeAxisRail()
{
    // Initialize Axis Rail with instant vertical movement using UE 5.6 axis system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Axis Rail..."));
    
    // Configure movement data for axis rail
    MovementData.MovementSpeed = 2000.0f; // Fastest rail
    MovementData.Acceleration = 5000.0f;  // Instant acceleration
    MovementData.Deceleration = 5000.0f;  // Instant deceleration
    MovementData.EnergyCostPerSecond = 12.0f; // Higher cost for instant movement
    
    // Configure visual properties
    VisualConfig.RailColor = FLinearColor(0.7f, 0.7f, 0.7f, 1.0f); // Silver/Gray
    VisualConfig.EffectIntensity = 0.8f;
    VisualConfig.bVisibleDuringDay = true;
    VisualConfig.bVisibleDuringNight = true; // Always visible
    
    // Load axis-specific assets
    static const FSoftObjectPath AxisMeshPath(TEXT("/Game/Environment/Rails/SM_AxisRail.SM_AxisRail"));
    if (UStaticMesh* AxisMesh = Cast<UStaticMesh>(AxisMeshPath.TryLoad()))
    {
        RailMesh->SetStaticMesh(AxisMesh);
    }
    
    // Load axis VFX
    static const FSoftObjectPath AxisVFXPath(TEXT("/Game/VFX/Rails/NS_AxisRailEffect.NS_AxisRailEffect"));
    if (UNiagaraSystem* AxisVFX = Cast<UNiagaraSystem>(AxisVFXPath.TryLoad()))
    {
        RailVFX->SetAsset(AxisVFX);
        VisualConfig.RailEffect = AxisVFX;
    }
    
    // Load axis audio
    static const FSoftObjectPath AxisAudioPath(TEXT("/Game/Audio/Rails/MS_AxisRailAmbient.MS_AxisRailAmbient"));
    if (UMetaSoundSource* AxisAudio = Cast<UMetaSoundSource>(AxisAudioPath.TryLoad()))
    {
        RailAudio->SetSound(AxisAudio);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Axis Rail initialized with instant vertical movement"));
}

void AAuracronDynamicRail::InitializeLunarRail()
{
    // Initialize Lunar Rail with ethereal stealth characteristics using UE 5.6 lunar system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Lunar Rail..."));
    
    // Configure movement data for lunar rail
    MovementData.MovementSpeed = 1200.0f;
    MovementData.Acceleration = 800.0f;
    MovementData.Deceleration = 600.0f;
    MovementData.EnergyCostPerSecond = 6.0f; // Lower cost for stealth
    
    // Configure visual properties
    VisualConfig.RailColor = FLinearColor(0.6f, 0.8f, 1.0f, 0.7f); // Ethereal blue-white
    VisualConfig.EffectIntensity = 0.9f;
    VisualConfig.bVisibleDuringDay = false; // Night only
    VisualConfig.bVisibleDuringNight = true;
    
    // Load lunar-specific assets
    static const FSoftObjectPath LunarMeshPath(TEXT("/Game/Environment/Rails/SM_LunarRail.SM_LunarRail"));
    if (UStaticMesh* LunarMesh = Cast<UStaticMesh>(LunarMeshPath.TryLoad()))
    {
        RailMesh->SetStaticMesh(LunarMesh);
    }
    
    // Load lunar VFX
    static const FSoftObjectPath LunarVFXPath(TEXT("/Game/VFX/Rails/NS_LunarRailEffect.NS_LunarRailEffect"));
    if (UNiagaraSystem* LunarVFX = Cast<UNiagaraSystem>(LunarVFXPath.TryLoad()))
    {
        RailVFX->SetAsset(LunarVFX);
        VisualConfig.RailEffect = LunarVFX;
    }
    
    // Load lunar audio
    static const FSoftObjectPath LunarAudioPath(TEXT("/Game/Audio/Rails/MS_LunarRailAmbient.MS_LunarRailAmbient"));
    if (UMetaSoundSource* LunarAudio = Cast<UMetaSoundSource>(LunarAudioPath.TryLoad()))
    {
        RailAudio->SetSound(LunarAudio);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Lunar Rail initialized with ethereal stealth characteristics"));
}

void AAuracronDynamicRail::ActivateRail()
{
    if (bIsActive)
    {
        return;
    }
    
    bIsActive = true;
    
    // Activate visual effects using UE 5.6 activation system
    if (RailVFX && VisualConfig.RailEffect)
    {
        RailVFX->Activate();
        
        // Set rail-specific VFX parameters
        RailVFX->SetVariableFloat(FName("RailIntensity"), VisualConfig.EffectIntensity);
        RailVFX->SetVariableLinearColor(FName("RailColor"), VisualConfig.RailColor);
        RailVFX->SetVariableFloat(FName("MovementSpeed"), MovementData.MovementSpeed);
    }
    
    // Activate audio
    if (RailAudio && RailAudio->GetSound())
    {
        RailAudio->Play();
        RailAudio->SetVolumeMultiplier(0.7f);
        RailAudio->SetFloatParameter(TEXT("RailIntensity"), VisualConfig.EffectIntensity);
    }
    
    // Play activation effects
    PlayRailActivationEffects();
    
    // Update visibility based on time of day
    UpdateRailVisibility();
    
    // Start player movement timer
    GetWorld()->GetTimerManager().SetTimer(
        PlayerMovementTimer,
        [this]()
        {
            for (APawn* Player : PlayersOnRail)
            {
                if (Player)
                {
                    UpdatePlayerMovement(Player, 0.033f); // ~30 FPS movement updates
                }
            }
        },
        0.033f, // ~30 FPS
        true    // Looping
    );
    
    // Broadcast activation event
    OnRailActivated();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Rail activated - Type: %s"), *UEnum::GetValueAsString(RailType));
}

void AAuracronDynamicRail::DeactivateRail()
{
    if (!bIsActive)
    {
        return;
    }
    
    // Stop all players on rail
    TArray<APawn*> PlayersToStop = PlayersOnRail;
    for (APawn* Player : PlayersToStop)
    {
        if (Player)
        {
            StopPlayerMovement(Player);
        }
    }
    
    bIsActive = false;
    
    // Deactivate visual effects
    if (RailVFX)
    {
        RailVFX->Deactivate();
    }
    
    // Stop audio
    if (RailAudio)
    {
        RailAudio->Stop();
    }
    
    // Clear movement timer
    GetWorld()->GetTimerManager().ClearTimer(PlayerMovementTimer);
    
    // Play deactivation effects
    PlayRailDeactivationEffects();
    
    // Broadcast deactivation event
    OnRailDeactivated();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Rail deactivated - Type: %s"), *UEnum::GetValueAsString(RailType));
}

void AAuracronDynamicRail::OnTriggerBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    // Handle player entering rail trigger using UE 5.6 overlap system
    if (APawn* Player = Cast<APawn>(OtherActor))
    {
        if (CanPlayerUseRail(Player))
        {
            // Add player to potential users
            PotentialUsers.AddUnique(Player);
            
            // Show rail interaction UI or effects
            ShowRailInteractionPrompt(Player);
            
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s entered rail trigger - Type: %s"), 
                *Player->GetName(), *UEnum::GetValueAsString(RailType));
        }
    }
}

void AAuracronDynamicRail::OnTriggerEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, int32 OtherBodyIndex)
{
    // Handle player leaving rail trigger
    if (APawn* Player = Cast<APawn>(OtherActor))
    {
        // Remove from potential users
        PotentialUsers.Remove(Player);
        
        // Hide rail interaction UI
        HideRailInteractionPrompt(Player);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s left rail trigger - Type: %s"), 
            *Player->GetName(), *UEnum::GetValueAsString(RailType));
    }
}

// === Player Movement System Implementation ===

bool AAuracronDynamicRail::CanPlayerUseRail(APawn* Player) const
{
    if (!Player || !bIsActive)
    {
        return false;
    }

    // Check if player is already on a rail
    if (IsPlayerOnRail(Player))
    {
        return false;
    }

    // Check rail-specific requirements using UE 5.6 validation system
    switch (RailType)
    {
        case EAuracronRailType::Solar:
            // Solar rails require daylight or sufficient energy
            return IsNightTime() ? HasSufficientEnergy(Player, MovementData.EnergyCostPerSecond * 2.0f) : true;

        case EAuracronRailType::Axis:
            // Axis rails require high energy due to instant movement
            return HasSufficientEnergy(Player, MovementData.EnergyCostPerSecond);

        case EAuracronRailType::Lunar:
            // Lunar rails only work at night
            return IsNightTime();

        default:
            return true;
    }
}

bool AAuracronDynamicRail::StartPlayerMovement(APawn* Player, bool bForwardDirection)
{
    if (!Player || !CanPlayerUseRail(Player))
    {
        return false;
    }

    // Add player to rail using UE 5.6 movement system
    PlayersOnRail.AddUnique(Player);
    PotentialUsers.Remove(Player);

    // Initialize player rail data
    FPlayerRailData RailData;
    RailData.Player = Player;
    RailData.Progress = bForwardDirection ? 0.0f : 1.0f;
    RailData.bMovingForward = bForwardDirection;
    RailData.CurrentSpeed = 0.0f;
    RailData.StartTime = GetWorld()->GetTimeSeconds();
    RailData.bIsActive = true;

    PlayerRailDataMap.Add(Player, RailData);

    // Apply rail entry effects using UE 5.6 effect system
    ApplyRailEntryEffects(Player);

    // Disable player's normal movement
    if (ACharacter* Character = Cast<ACharacter>(Player))
    {
        if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
        {
            MovementComp->DisableMovement();
            MovementComp->SetMovementMode(MOVE_Custom);
        }
    }

    // Position player at rail start/end
    FVector StartPosition = bForwardDirection ? GetRailStartPosition() : GetRailEndPosition();
    Player->SetActorLocation(StartPosition);

    // Broadcast player entered event
    OnPlayerEnteredRail(Player);

    ActivePlayerCount++;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s started movement on %s rail"),
        *Player->GetName(), *UEnum::GetValueAsString(RailType));

    return true;
}

void AAuracronDynamicRail::StopPlayerMovement(APawn* Player)
{
    if (!Player || !IsPlayerOnRail(Player))
    {
        return;
    }

    // Remove player from rail
    PlayersOnRail.Remove(Player);
    PlayerRailDataMap.Remove(Player);

    // Restore player's normal movement
    if (ACharacter* Character = Cast<ACharacter>(Player))
    {
        if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
        {
            MovementComp->SetMovementMode(MOVE_Walking);
        }
    }

    // Remove rail effects from player
    RemoveRailEffectsFromPlayer(Player);

    // Broadcast player exited event
    OnPlayerExitedRail(Player);

    ActivePlayerCount = FMath::Max(0, ActivePlayerCount - 1);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s stopped movement on %s rail"),
        *Player->GetName(), *UEnum::GetValueAsString(RailType));
}

void AAuracronDynamicRail::UpdatePlayerMovement(APawn* Player, float DeltaTime)
{
    if (!Player || !IsPlayerOnRail(Player))
    {
        return;
    }

    FPlayerRailData* RailData = PlayerRailDataMap.Find(Player);
    if (!RailData)
    {
        return;
    }

    // Update player movement along rail using UE 5.6 movement system
    float TargetSpeed = MovementData.MovementSpeed;

    // Apply time of day modifiers
    float TimeModifier = GetCurrentTimeOfDayMultiplier();
    TargetSpeed *= TimeModifier;

    // Apply rail type-specific modifiers
    TargetSpeed *= GetRailTypeSpeedModifier();

    // Smooth acceleration/deceleration
    float AccelRate = RailData->CurrentSpeed < TargetSpeed ? MovementData.Acceleration : MovementData.Deceleration;
    RailData->CurrentSpeed = FMath::FInterpTo(RailData->CurrentSpeed, TargetSpeed, DeltaTime, AccelRate / 1000.0f);

    // Calculate movement progress
    float SplineLength = RailSpline ? RailSpline->GetSplineLength() : 0.0f;
    float MovementDistance = RailData->CurrentSpeed * DeltaTime;
    float ProgressDelta = MovementDistance / SplineLength;

    if (!RailData->bMovingForward)
    {
        ProgressDelta = -ProgressDelta;
    }

    RailData->Progress += ProgressDelta;
    RailData->Progress = FMath::Clamp(RailData->Progress, 0.0f, 1.0f);

    // Calculate new position and rotation
    FVector NewLocation = CalculateRailPosition(RailData->Progress);
    FRotator NewRotation = CalculateRailRotation(RailData->Progress);

    // Apply movement to player
    ApplyRailMovementToPlayer(Player, NewLocation, NewRotation);

    // Apply rail type-specific effects during movement
    ApplyMovementEffects(Player, RailData, DeltaTime);

    // Check if player reached end of rail
    if (RailData->Progress <= 0.0f || RailData->Progress >= 1.0f)
    {
        // Handle rail completion
        HandleRailCompletion(Player, RailData);
    }

    // Consume energy
    ConsumePlayerEnergy(Player, MovementData.EnergyCostPerSecond * DeltaTime);
}

void AAuracronDynamicRail::ApplyRailMovementToPlayer(APawn* Player, const FVector& NewLocation, const FRotator& NewRotation)
{
    if (!Player)
    {
        return;
    }

    // Apply movement using UE 5.6 smooth movement system
    Player->SetActorLocation(NewLocation, true); // Sweep for collision
    Player->SetActorRotation(NewRotation);

    // Apply rail-specific movement effects
    switch (RailType)
    {
        case EAuracronRailType::Solar:
            // Apply heat distortion effect to player
            ApplyHeatDistortionToPlayer(Player);
            break;

        case EAuracronRailType::Axis:
            // Apply instant movement effect
            ApplyInstantMovementToPlayer(Player);
            break;

        case EAuracronRailType::Lunar:
            // Apply stealth effect
            ApplyStealthEffectToPlayer(Player);
            break;

        default:
            break;
    }
}

void AAuracronDynamicRail::ApplyRailEntryEffects(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Apply rail entry effects using UE 5.6 GameplayEffect system
    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = PlayerASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        // Apply rail-specific entry effects
        FString EffectPath;
        switch (RailType)
        {
            case EAuracronRailType::Solar:
                EffectPath = TEXT("/Game/GameplayEffects/Rails/GE_SolarRailEntry.GE_SolarRailEntry_C");
                break;
            case EAuracronRailType::Axis:
                EffectPath = TEXT("/Game/GameplayEffects/Rails/GE_AxisRailEntry.GE_AxisRailEntry_C");
                break;
            case EAuracronRailType::Lunar:
                EffectPath = TEXT("/Game/GameplayEffects/Rails/GE_LunarRailEntry.GE_LunarRailEntry_C");
                break;
            default:
                return;
        }

        FSoftClassPath RailEffectPath(EffectPath);
        if (TSubclassOf<UGameplayEffect> RailEffectClass = RailEffectPath.TryLoadClass<UGameplayEffect>())
        {
            const UGameplayEffect* RailEffect = RailEffectClass.GetDefaultObject();
            if (RailEffect)
            {
                FGameplayEffectSpec RailSpec(RailEffect, EffectContext, 1.0f);
            RailSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Rail.MovementSpeed")), MovementData.MovementSpeed);
            RailSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Rail.EffectIntensity")), VisualConfig.EffectIntensity);
            RailSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

                FActiveGameplayEffectHandle EffectHandle = PlayerASC->ApplyGameplayEffectSpecToSelf(RailSpec);
                PlayerRailEffects.Add(Player, EffectHandle);
            }
        }
    }

    // Spawn entry VFX
    static const FSoftObjectPath EntryVFXPath(TEXT("/Game/VFX/Rails/NS_RailEntry.NS_RailEntry"));
    if (UNiagaraSystem* EntryVFX = Cast<UNiagaraSystem>(EntryVFXPath.TryLoad()))
    {
        UNiagaraComponent* EntryComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            EntryVFX,
            Player->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true // Auto-destroy
        );

        if (EntryComponent)
        {
            EntryComponent->SetVariableLinearColor(FName("RailColor"), VisualConfig.RailColor);
            EntryComponent->SetVariableFloat(FName("EntryIntensity"), VisualConfig.EffectIntensity);
        }
    }
}

// === Rail Type-Specific Behavior Implementation ===

void AAuracronDynamicRail::UpdateSolarRailBehavior(float DeltaTime)
{
    // Update Solar Rail behavior with heat distortion and light particles using UE 5.6 solar system

    // Update heat distortion effect based on time of day
    float TimeOfDayMultiplier = GetCurrentTimeOfDayMultiplier();
    float HeatIntensity = VisualConfig.EffectIntensity * TimeOfDayMultiplier;

    if (RailVFX)
    {
        RailVFX->SetVariableFloat(FName("HeatDistortion"), HeatIntensity);
        RailVFX->SetVariableFloat(FName("LightParticleCount"), HeatIntensity * 100.0f);
        RailVFX->SetVariableLinearColor(FName("SolarColor"), FLinearColor(1.0f, 0.8f, 0.0f, HeatIntensity));
    }

    // Update solar rail materials
    UpdateSolarRailEffects(DeltaTime);

    // Apply speed boost during peak daylight
    float CurrentHour = GetCurrentGameHour();
    if (CurrentHour >= 10.0f && CurrentHour <= 14.0f) // Peak daylight hours
    {
        // Apply speed boost to all players on rail
        for (APawn* Player : PlayersOnRail)
        {
            if (FPlayerRailData* RailData = PlayerRailDataMap.Find(Player))
            {
                RailData->CurrentSpeed *= 1.3f; // 30% speed boost during peak hours
            }
        }
    }

    // Update heat wave effects
    if (HeatIntensity > 0.8f)
    {
        ApplyHeatWaveEffects();
    }
}

void AAuracronDynamicRail::UpdateAxisRailBehavior(float DeltaTime)
{
    // Update Axis Rail behavior with instant vertical movement using UE 5.6 axis system

    // Axis rails maintain constant efficiency
    if (RailVFX)
    {
        RailVFX->SetVariableFloat(FName("AxisStability"), 1.0f);
        RailVFX->SetVariableFloat(FName("VerticalForce"), MovementData.MovementSpeed * 0.001f);
        RailVFX->SetVariableLinearColor(FName("AxisColor"), FLinearColor(0.7f, 0.7f, 0.7f, 1.0f));
    }

    // Update axis rail effects
    UpdateAxisRailEffects(DeltaTime);

    // Handle instant vertical movement for players
    for (APawn* Player : PlayersOnRail)
    {
        if (FPlayerRailData* RailData = PlayerRailDataMap.Find(Player))
        {
            // Check if player is moving vertically (between layers)
            FVector CurrentPos = CalculateRailPosition(RailData->Progress);
            FVector PreviousPos = CalculateRailPosition(FMath::Max(0.0f, RailData->Progress - 0.1f));

            float VerticalDifference = FMath::Abs(CurrentPos.Z - PreviousPos.Z);

            if (VerticalDifference > 100.0f) // Significant vertical movement
            {
                // Apply instant vertical movement
                RailData->CurrentSpeed = MovementData.MovementSpeed * 2.0f; // Double speed for vertical

                // Apply axis-specific effects
                ApplyAxisVerticalEffects(Player);
            }
        }
    }

    // Update neutral appearance effects
    ApplyNeutralAxisEffects();
}

void AAuracronDynamicRail::UpdateLunarRailBehavior(float DeltaTime)
{
    // Update Lunar Rail behavior with ethereal glow and stealth using UE 5.6 lunar system

    // Lunar rails only work at night
    bool bIsNight = IsNightTime();

    if (!bIsNight)
    {
        // Fade out during day
        if (RailVFX)
        {
            float FadeAlpha = FMath::Max(0.0f, VisualConfig.EffectIntensity - DeltaTime);
            RailVFX->SetVariableFloat(FName("EtherealGlow"), FadeAlpha);
            VisualConfig.EffectIntensity = FadeAlpha;
        }

        // Stop all players if rail becomes inactive during day
        if (VisualConfig.EffectIntensity <= 0.1f)
        {
            TArray<APawn*> PlayersToStop = PlayersOnRail;
            for (APawn* Player : PlayersToStop)
            {
                StopPlayerMovement(Player);
            }
        }

        return;
    }

    // Night behavior - enhance ethereal effects
    float NightIntensity = CalculateNightIntensity();

    if (RailVFX)
    {
        RailVFX->SetVariableFloat(FName("EtherealGlow"), NightIntensity);
        RailVFX->SetVariableFloat(FName("StealthBonus"), NightIntensity * 0.8f);
        RailVFX->SetVariableLinearColor(FName("LunarColor"), FLinearColor(0.6f, 0.8f, 1.0f, NightIntensity));
    }

    // Update lunar rail effects
    UpdateLunarRailEffects(DeltaTime);

    // Apply stealth bonus to players on rail
    for (APawn* Player : PlayersOnRail)
    {
        if (Player)
        {
            ApplyLunarStealthBonus(Player, NightIntensity);
        }
    }

    // Apply ethereal movement enhancement
    ApplyEtherealMovementEnhancement(NightIntensity);
}

// === Rail Type-Specific Effects Implementation ===

void AAuracronDynamicRail::UpdateSolarRailEffects(float DeltaTime)
{
    // Update solar-specific effects using UE 5.6 solar effect system
    float TimeOfDayMultiplier = GetCurrentTimeOfDayMultiplier();

    // Update dynamic materials for solar rail
    for (UMaterialInstanceDynamic* DynamicMaterial : DynamicRailMaterials)
    {
        if (DynamicMaterial)
        {
            DynamicMaterial->SetScalarParameterValue(TEXT("SolarIntensity"), TimeOfDayMultiplier);
            DynamicMaterial->SetScalarParameterValue(TEXT("HeatDistortion"), TimeOfDayMultiplier * 1.5f);
            DynamicMaterial->SetVectorParameterValue(TEXT("SolarColor"), FLinearColor(1.0f, 0.8f, 0.0f, TimeOfDayMultiplier));

            // Add pulsing effect
            float PulseValue = (FMath::Sin(LastUpdateTime * 3.0f) + 1.0f) * 0.5f;
            DynamicMaterial->SetScalarParameterValue(TEXT("SolarPulse"), PulseValue * TimeOfDayMultiplier);
        }
    }

    // Update lighting for solar rail
    TArray<ULightComponent*> LightComponents;
    GetComponents<ULightComponent>(LightComponents);

    for (ULightComponent* LightComp : LightComponents)
    {
        if (LightComp)
        {
            float BaseLightIntensity = 2000.0f;
            LightComp->SetIntensity(BaseLightIntensity * TimeOfDayMultiplier);
            LightComp->SetLightColor(FLinearColor(1.0f, 0.9f, 0.6f));
        }
    }
}

void AAuracronDynamicRail::UpdateAxisRailEffects(float DeltaTime)
{
    // Update axis-specific effects using UE 5.6 axis effect system

    // Axis rails maintain constant neutral appearance
    for (UMaterialInstanceDynamic* DynamicMaterial : DynamicRailMaterials)
    {
        if (DynamicMaterial)
        {
            DynamicMaterial->SetScalarParameterValue(TEXT("AxisStability"), 1.0f);
            DynamicMaterial->SetScalarParameterValue(TEXT("NeutralIntensity"), VisualConfig.EffectIntensity);
            DynamicMaterial->SetVectorParameterValue(TEXT("AxisColor"), FLinearColor(0.7f, 0.7f, 0.7f, 1.0f));

            // Add subtle energy flow effect
            float FlowValue = FMath::Fmod(LastUpdateTime * 2.0f, 1.0f);
            DynamicMaterial->SetScalarParameterValue(TEXT("EnergyFlow"), FlowValue);
        }
    }

    // Update axis lighting - constant intensity
    TArray<ULightComponent*> LightComponents;
    GetComponents<ULightComponent>(LightComponents);

    for (ULightComponent* LightComp : LightComponents)
    {
        if (LightComp)
        {
            LightComp->SetIntensity(1500.0f); // Constant intensity
            LightComp->SetLightColor(FLinearColor(0.8f, 0.8f, 0.8f));
        }
    }
}

void AAuracronDynamicRail::UpdateLunarRailEffects(float DeltaTime)
{
    // Update lunar-specific effects using UE 5.6 lunar effect system
    bool bIsNight = IsNightTime();
    float NightIntensity = bIsNight ? CalculateNightIntensity() : 0.0f;

    // Update dynamic materials for lunar rail
    for (UMaterialInstanceDynamic* DynamicMaterial : DynamicRailMaterials)
    {
        if (DynamicMaterial)
        {
            DynamicMaterial->SetScalarParameterValue(TEXT("EtherealGlow"), NightIntensity);
            DynamicMaterial->SetScalarParameterValue(TEXT("LunarPhase"), GetCurrentLunarPhase());
            DynamicMaterial->SetVectorParameterValue(TEXT("LunarColor"), FLinearColor(0.6f, 0.8f, 1.0f, NightIntensity));

            // Add ethereal shimmer effect
            float ShimmerValue = (FMath::Sin(LastUpdateTime * 4.0f) + 1.0f) * 0.5f;
            DynamicMaterial->SetScalarParameterValue(TEXT("EtherealShimmer"), ShimmerValue * NightIntensity);
        }
    }

    // Update lunar lighting
    TArray<ULightComponent*> LightComponents;
    GetComponents<ULightComponent>(LightComponents);

    for (ULightComponent* LightComp : LightComponents)
    {
        if (LightComp)
        {
            float LunarLightIntensity = 1200.0f * NightIntensity;
            LightComp->SetIntensity(LunarLightIntensity);
            LightComp->SetLightColor(FLinearColor(0.7f, 0.9f, 1.0f));
        }
    }
}

// === Rail-Specific Player Effects Implementation ===

void AAuracronDynamicRail::ApplyHeatDistortionToPlayer(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Apply heat distortion effect to player using UE 5.6 distortion system
    static const FSoftObjectPath HeatDistortionVFXPath(TEXT("/Game/VFX/Rails/Solar/NS_HeatDistortion.NS_HeatDistortion"));
    if (UNiagaraSystem* HeatVFX = Cast<UNiagaraSystem>(HeatDistortionVFXPath.TryLoad()))
    {
        UNiagaraComponent* HeatComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            HeatVFX,
            Player->GetRootComponent(),
            NAME_None,
            FVector(0.0f, 0.0f, 50.0f),
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            false
        );

        if (HeatComponent)
        {
            float HeatIntensity = GetCurrentTimeOfDayMultiplier();
            HeatComponent->SetVariableFloat(FName("DistortionStrength"), HeatIntensity);
            HeatComponent->SetVariableFloat(FName("HeatRadius"), 150.0f);
            HeatComponent->SetVariableLinearColor(FName("HeatColor"), FLinearColor(1.0f, 0.6f, 0.0f, HeatIntensity * 0.5f));
        }
    }
}

void AAuracronDynamicRail::ApplyInstantMovementToPlayer(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Apply instant movement effect using UE 5.6 instant movement system
    static const FSoftObjectPath InstantVFXPath(TEXT("/Game/VFX/Rails/Axis/NS_InstantMovement.NS_InstantMovement"));
    if (UNiagaraSystem* InstantVFX = Cast<UNiagaraSystem>(InstantVFXPath.TryLoad()))
    {
        UNiagaraComponent* InstantComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            InstantVFX,
            Player->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true // Auto-destroy
        );

        if (InstantComponent)
        {
            InstantComponent->SetVariableFloat(FName("MovementSpeed"), MovementData.MovementSpeed);
            InstantComponent->SetVariableLinearColor(FName("InstantColor"), FLinearColor(0.9f, 0.9f, 0.9f, 0.8f));
        }
    }

    // Apply temporary invulnerability during instant movement
    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        static const FSoftClassPath InstantMovementEffectPath(TEXT("/Game/GameplayEffects/Rails/GE_InstantMovementProtection.GE_InstantMovementProtection_C"));
        if (TSubclassOf<UGameplayEffect> InstantEffectClass = InstantMovementEffectPath.TryLoadClass<UGameplayEffect>())
        {
            const UGameplayEffect* InstantEffect = InstantEffectClass.GetDefaultObject();
            if (InstantEffect)
            {
                FGameplayEffectContextHandle EffectContext = PlayerASC->MakeEffectContext();
                EffectContext.AddSourceObject(this);

                FGameplayEffectSpec InstantSpec(InstantEffect, EffectContext, 1.0f);
            InstantSpec.SetDuration(0.5f, false); // Brief protection

                PlayerASC->ApplyGameplayEffectSpecToSelf(InstantSpec);
            }
        }
    }
}

void AAuracronDynamicRail::ApplyStealthEffectToPlayer(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Apply stealth effect using UE 5.6 stealth system
    float NightIntensity = CalculateNightIntensity();

    // Apply visual stealth effect
    static const FSoftObjectPath StealthVFXPath(TEXT("/Game/VFX/Rails/Lunar/NS_LunarStealth.NS_LunarStealth"));
    if (UNiagaraSystem* StealthVFX = Cast<UNiagaraSystem>(StealthVFXPath.TryLoad()))
    {
        UNiagaraComponent* StealthComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            StealthVFX,
            Player->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            false
        );

        if (StealthComponent)
        {
            StealthComponent->SetVariableFloat(FName("StealthLevel"), NightIntensity);
            StealthComponent->SetVariableLinearColor(FName("StealthColor"), FLinearColor(0.6f, 0.8f, 1.0f, NightIntensity * 0.3f));
        }
    }

    // Apply gameplay stealth effect
    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        static const FSoftClassPath StealthEffectPath(TEXT("/Game/GameplayEffects/Rails/GE_LunarStealth.GE_LunarStealth_C"));
        if (TSubclassOf<UGameplayEffect> StealthEffectClass = StealthEffectPath.TryLoadClass<UGameplayEffect>())
        {
            const UGameplayEffect* StealthEffect = StealthEffectClass.GetDefaultObject();
            if (StealthEffect)
            {
                FGameplayEffectContextHandle EffectContext = PlayerASC->MakeEffectContext();
                EffectContext.AddSourceObject(this);

                FGameplayEffectSpec StealthSpec(StealthEffect, EffectContext, 1.0f);
            StealthSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Stealth.Bonus")), NightIntensity);
            StealthSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Stealth.MovementSpeed")), NightIntensity * 0.5f);
            StealthSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

                FActiveGameplayEffectHandle StealthHandle = PlayerASC->ApplyGameplayEffectSpecToSelf(StealthSpec);
                PlayerStealthEffects.Add(Player, StealthHandle);
            }
        }
    }
}

void AAuracronDynamicRail::ApplyLunarStealthBonus(APawn* Player, float NightIntensity)
{
    if (!Player)
    {
        return;
    }

    // Apply lunar stealth bonus using UE 5.6 stealth bonus system
    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Update existing stealth effect or apply new one
        if (FActiveGameplayEffectHandle* ExistingHandle = PlayerStealthEffects.Find(Player))
        {
            // Update existing effect parameters
            if (PlayerASC->GetActiveGameplayEffect(*ExistingHandle))
            {
                // Effect is still active, parameters will be updated by the effect itself
                return;
            }
        }

        // Apply new stealth effect
        static const FSoftClassPath LunarStealthPath(TEXT("/Game/GameplayEffects/Rails/GE_LunarStealthBonus.GE_LunarStealthBonus_C"));
        if (TSubclassOf<UGameplayEffect> LunarStealthEffectClass = LunarStealthPath.TryLoadClass<UGameplayEffect>())
        {
            const UGameplayEffect* LunarStealthEffect = LunarStealthEffectClass.GetDefaultObject();
            if (LunarStealthEffect)
            {
                FGameplayEffectContextHandle EffectContext = PlayerASC->MakeEffectContext();
                EffectContext.AddSourceObject(this);

                FGameplayEffectSpec StealthSpec(LunarStealthEffect, EffectContext, 1.0f);
            StealthSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Lunar.StealthBonus")), NightIntensity);
            StealthSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Lunar.SpeedBonus")), NightIntensity * 0.3f);
            StealthSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Lunar.VisibilityReduction")), NightIntensity * 0.6f);
            StealthSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

                FActiveGameplayEffectHandle StealthHandle = PlayerASC->ApplyGameplayEffectSpecToSelf(StealthSpec);
                PlayerStealthEffects.Add(Player, StealthHandle);
            }
        }
    }
}

// === Utility Methods Implementation ===

float AAuracronDynamicRail::GetCurrentTimeOfDayMultiplier() const
{
    // Calculate time of day multiplier using UE 5.6 time system
    if (!GetWorld())
    {
        return 1.0f;
    }

    // Get current game time (assuming 24-hour cycle)
    float CurrentHour = GetCurrentGameHour();

    // Calculate solar efficiency based on time
    if (RailType == EAuracronRailType::Solar)
    {
        // Peak efficiency at noon (12:00), minimum at midnight (0:00/24:00)
        float SolarEfficiency = FMath::Cos((CurrentHour - 12.0f) * PI / 12.0f);
        return FMath::Clamp(SolarEfficiency, 0.1f, 1.5f);
    }
    else if (RailType == EAuracronRailType::Lunar)
    {
        // Peak efficiency at midnight, minimum at noon
        float LunarEfficiency = FMath::Cos((CurrentHour) * PI / 12.0f);
        return FMath::Clamp(-LunarEfficiency, 0.0f, 1.3f);
    }

    // Axis rails are not affected by time
    return 1.0f;
}

bool AAuracronDynamicRail::IsNightTime() const
{
    // Determine if it's night time using UE 5.6 time system
    float CurrentHour = GetCurrentGameHour();
    return (CurrentHour < 6.0f || CurrentHour > 18.0f);
}

float AAuracronDynamicRail::GetCurrentGameHour() const
{
    // Get current game hour using UE 5.6 time system
    if (!GetWorld())
    {
        return 12.0f; // Default to noon
    }

    // Calculate hour based on game time (24-hour cycle over 24 minutes real time)
    float GameTimeSeconds = GetWorld()->GetTimeSeconds();
    float GameHour = FMath::Fmod(GameTimeSeconds / 60.0f, 24.0f); // 1 minute = 1 hour

    return GameHour;
}

float AAuracronDynamicRail::CalculateNightIntensity() const
{
    // Calculate night intensity for lunar effects
    if (!IsNightTime())
    {
        return 0.0f;
    }

    float CurrentHour = GetCurrentGameHour();

    // Peak intensity at midnight (0:00), fade towards dawn/dusk
    float MidnightDistance = FMath::Min(CurrentHour, 24.0f - CurrentHour);
    float NightIntensity = 1.0f - (MidnightDistance / 6.0f); // Fade over 6 hours

    return FMath::Clamp(NightIntensity, 0.0f, 1.0f);
}

float AAuracronDynamicRail::GetCurrentLunarPhase() const
{
    // Calculate lunar phase for visual effects (0.0 = new moon, 1.0 = full moon)
    float GameTimeSeconds = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    float LunarCycle = FMath::Fmod(GameTimeSeconds / 3600.0f, 1.0f); // 1 hour = 1 lunar cycle

    return (FMath::Sin(LunarCycle * 2.0f * PI) + 1.0f) * 0.5f;
}

// === Rail Construction and Configuration Implementation ===

void AAuracronDynamicRail::BuildRailMesh()
{
    if (!RailMesh || !RailSpline)
    {
        return;
    }

    // Build rail mesh along spline using UE 5.6 spline mesh system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Building rail mesh for %s rail"), *UEnum::GetValueAsString(RailType));

    // Create dynamic material for rail
    if (UMaterialInterface* BaseMaterial = RailMesh->GetMaterial(0))
    {
        UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);

        // Set rail type-specific material parameters
        DynamicMaterial->SetVectorParameterValue(TEXT("RailColor"), VisualConfig.RailColor);
        DynamicMaterial->SetScalarParameterValue(TEXT("EffectIntensity"), VisualConfig.EffectIntensity);
        DynamicMaterial->SetScalarParameterValue(TEXT("RailType"), static_cast<float>(RailType));

        RailMesh->SetMaterial(0, DynamicMaterial);
        DynamicRailMaterials.Add(DynamicMaterial);
    }

    // Configure mesh properties based on rail type
    switch (RailType)
    {
        case EAuracronRailType::Solar:
            RailMesh->SetCastShadow(true);
            RailMesh->SetReceivesDecals(false);
            break;
        case EAuracronRailType::Axis:
            RailMesh->SetCastShadow(true);
            RailMesh->SetReceivesDecals(true);
            break;
        case EAuracronRailType::Lunar:
            RailMesh->SetCastShadow(false); // Ethereal, no shadows
            RailMesh->SetReceivesDecals(false);
            break;
        default:
            break;
    }
}

void AAuracronDynamicRail::SetupRailEffects()
{
    if (!RailVFX)
    {
        return;
    }

    // Setup rail effects using UE 5.6 Niagara system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up effects for %s rail"), *UEnum::GetValueAsString(RailType));

    // Configure VFX based on rail type
    if (VisualConfig.RailEffect)
    {
        RailVFX->SetAsset(VisualConfig.RailEffect);

        // Set common VFX parameters
        RailVFX->SetVariableFloat(FName("RailLength"), RailSpline ? RailSpline->GetSplineLength() : 0.0f);
        RailVFX->SetVariableLinearColor(FName("RailColor"), VisualConfig.RailColor);
        RailVFX->SetVariableFloat(FName("EffectIntensity"), VisualConfig.EffectIntensity);
        RailVFX->SetVariableFloat(FName("MovementSpeed"), MovementData.MovementSpeed);

        // Set rail type-specific parameters
        switch (RailType)
        {
            case EAuracronRailType::Solar:
                RailVFX->SetVariableFloat(FName("HeatDistortion"), 1.0f);
                RailVFX->SetVariableFloat(FName("LightParticleCount"), 200.0f);
                RailVFX->SetVariableFloat(FName("SolarFlareChance"), 0.1f);
                break;

            case EAuracronRailType::Axis:
                RailVFX->SetVariableFloat(FName("AxisStability"), 1.0f);
                RailVFX->SetVariableFloat(FName("NeutralGlow"), 0.8f);
                RailVFX->SetVariableFloat(FName("VerticalForceIndicator"), 1.0f);
                break;

            case EAuracronRailType::Lunar:
                RailVFX->SetVariableFloat(FName("EtherealGlow"), 1.0f);
                RailVFX->SetVariableFloat(FName("LunarPhase"), GetCurrentLunarPhase());
                RailVFX->SetVariableFloat(FName("StealthParticles"), 150.0f);
                break;

            default:
                break;
        }
    }

    // Setup activation effect
    if (VisualConfig.ActivationEffect)
    {
        // Store activation effect for later use
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Activation effect configured for %s rail"), *UEnum::GetValueAsString(RailType));
    }
}

void AAuracronDynamicRail::ConfigureRailAudio()
{
    if (!RailAudio)
    {
        return;
    }

    // Configure rail audio using UE 5.6 MetaSound system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Configuring audio for %s rail"), *UEnum::GetValueAsString(RailType));

    // Set common audio parameters
    RailAudio->SetVolumeMultiplier(0.7f);
    RailAudio->SetPitchMultiplier(1.0f);
    RailAudio->bAutoActivate = false;

    // Set rail type-specific audio parameters
    switch (RailType)
    {
        case EAuracronRailType::Solar:
            RailAudio->SetFloatParameter(TEXT("SolarIntensity"), VisualConfig.EffectIntensity);
            RailAudio->SetFloatParameter(TEXT("HeatLevel"), 1.0f);
            RailAudio->SetFloatParameter(TEXT("EnergyHum"), MovementData.MovementSpeed * 0.001f);
            break;

        case EAuracronRailType::Axis:
            RailAudio->SetFloatParameter(TEXT("AxisStability"), 1.0f);
            RailAudio->SetFloatParameter(TEXT("MechanicalHum"), 0.8f);
            RailAudio->SetFloatParameter(TEXT("InstantMovementCharge"), 1.0f);
            break;

        case EAuracronRailType::Lunar:
            RailAudio->SetFloatParameter(TEXT("EtherealResonance"), VisualConfig.EffectIntensity);
            RailAudio->SetFloatParameter(TEXT("LunarPhase"), GetCurrentLunarPhase());
            RailAudio->SetFloatParameter(TEXT("StealthWhisper"), 0.6f);
            break;

        default:
            break;
    }
}

void AAuracronDynamicRail::CreateEntryExitPoints()
{
    if (!RailSpline)
    {
        return;
    }

    // Create entry and exit points using UE 5.6 spline system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creating entry/exit points for %s rail"), *UEnum::GetValueAsString(RailType));

    int32 SplinePoints = RailSpline->GetNumberOfSplinePoints();

    if (SplinePoints >= 2)
    {
        // Create entry point at start
        FVector EntryLocation = RailSpline->GetLocationAtSplinePoint(0, ESplineCoordinateSpace::World);
        CreateRailAccessPoint(EntryLocation, true); // Entry point

        // Create exit point at end
        FVector ExitLocation = RailSpline->GetLocationAtSplinePoint(SplinePoints - 1, ESplineCoordinateSpace::World);
        CreateRailAccessPoint(ExitLocation, false); // Exit point

        // Create intermediate access points for longer rails
        float CurrentSplineLength = RailSpline ? RailSpline->GetSplineLength() : 0.0f;
        if (CurrentSplineLength > 2000.0f)
        {
            int32 IntermediatePoints = FMath::FloorToInt(CurrentSplineLength / 1000.0f);
            for (int32 i = 1; i < IntermediatePoints; i++)
            {
                float Progress = static_cast<float>(i) / static_cast<float>(IntermediatePoints);
                FVector IntermediateLocation = RailSpline->GetLocationAtDistanceAlongSpline(Progress * CurrentSplineLength, ESplineCoordinateSpace::World);
                CreateRailAccessPoint(IntermediateLocation, true); // Can be both entry and exit
            }
        }
    }
}

void AAuracronDynamicRail::CreateRailAccessPoint(const FVector& Location, bool bIsEntryPoint)
{
    // Create access point actor using UE 5.6 spawning system
    FActorSpawnParameters SpawnParams;
    SpawnParams.Owner = this;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    static const FSoftClassPath AccessPointPath(TEXT("/Game/Environment/Rails/BP_RailAccessPoint.BP_RailAccessPoint_C"));
    if (UClass* AccessPointClass = AccessPointPath.TryLoadClass<AActor>())
    {
        if (AActor* AccessPoint = GetWorld()->SpawnActor<AActor>(AccessPointClass, Location, FRotator::ZeroRotator, SpawnParams))
        {
            // Configure access point
            if (UStaticMeshComponent* AccessMesh = AccessPoint->FindComponentByClass<UStaticMeshComponent>())
            {
                // Apply rail type-specific material
                if (UMaterialInterface* AccessMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Game/Materials/Rails/M_RailAccessPoint")))
                {
                    UMaterialInstanceDynamic* DynamicAccessMaterial = UMaterialInstanceDynamic::Create(AccessMaterial, AccessPoint);
                    DynamicAccessMaterial->SetVectorParameterValue(TEXT("AccessPointColor"), VisualConfig.RailColor);
                    DynamicAccessMaterial->SetScalarParameterValue(TEXT("IsEntryPoint"), bIsEntryPoint ? 1.0f : 0.0f);
                    AccessMesh->SetMaterial(0, DynamicAccessMaterial);
                }
            }

            // Add access point VFX
            static const FSoftObjectPath AccessVFXPath(TEXT("/Game/VFX/Rails/NS_RailAccessPoint.NS_RailAccessPoint"));
            if (UNiagaraSystem* AccessVFX = Cast<UNiagaraSystem>(AccessVFXPath.TryLoad()))
            {
                UNiagaraComponent* AccessVFXComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                    AccessVFX,
                    AccessPoint->GetRootComponent(),
                    NAME_None,
                    FVector(0.0f, 0.0f, 50.0f),
                    FRotator::ZeroRotator,
                    EAttachLocation::KeepRelativeOffset,
                    false
                );

                if (AccessVFXComponent)
                {
                    AccessVFXComponent->SetVariableLinearColor(FName("AccessColor"), VisualConfig.RailColor);
                    AccessVFXComponent->SetVariableFloat(FName("AccessIntensity"), VisualConfig.EffectIntensity);
                }
            }

            // Create and store access point collision component
            USphereComponent* AccessSphere = NewObject<USphereComponent>(this);
            AccessSphere->SetupAttachment(RootComponent);
            AccessSphere->SetWorldLocation(Location);
            AccessSphere->SetSphereRadius(100.0f);
            AccessSphere->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            AccessSphere->SetCollisionResponseToAllChannels(ECR_Ignore);
            AccessSphere->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);

            if (bIsEntryPoint)
            {
                EntryPoints.Add(AccessSphere);
            }
            else
            {
                ExitPoints.Add(AccessSphere);
            }
        }
    }
}

// === Rail Position and Movement Calculation Implementation ===

FVector AAuracronDynamicRail::CalculateRailPosition(float Progress) const
{
    if (!RailSpline)
    {
        return GetActorLocation();
    }

    // Calculate position along spline using UE 5.6 spline system
    float SplineLength = RailSpline ? RailSpline->GetSplineLength() : 0.0f;
    float DistanceAlongSpline = Progress * SplineLength;

    FVector SplinePosition = RailSpline->GetLocationAtDistanceAlongSpline(DistanceAlongSpline, ESplineCoordinateSpace::World);

    // Apply rail type-specific position modifications
    switch (RailType)
    {
        case EAuracronRailType::Solar:
            // Add slight vertical oscillation for heat effect
            {
                float HeatOscillation = FMath::Sin(Progress * 10.0f + LastUpdateTime * 2.0f) * 20.0f;
                SplinePosition.Z += HeatOscillation * GetCurrentTimeOfDayMultiplier();
            }
            break;

        case EAuracronRailType::Axis:
            // Axis rails maintain precise positioning
            break;

        case EAuracronRailType::Lunar:
            // Add ethereal floating effect
            {
                float EtherealFloat = FMath::Sin(Progress * 8.0f + LastUpdateTime * 1.5f) * 30.0f;
                SplinePosition.Z += EtherealFloat * CalculateNightIntensity();
            }
            break;

        default:
            break;
    }

    return SplinePosition;
}

FRotator AAuracronDynamicRail::CalculateRailRotation(float Progress) const
{
    if (!RailSpline)
    {
        return GetActorRotation();
    }

    // Calculate rotation along spline using UE 5.6 spline rotation system
    float SplineLength = RailSpline ? RailSpline->GetSplineLength() : 0.0f;
    float DistanceAlongSpline = Progress * SplineLength;

    FRotator SplineRotation = RailSpline->GetRotationAtDistanceAlongSpline(DistanceAlongSpline, ESplineCoordinateSpace::World);

    // Apply rail type-specific rotation modifications
    switch (RailType)
    {
        case EAuracronRailType::Solar:
            // Add slight banking for heat effect
            {
                float HeatBanking = FMath::Sin(Progress * 6.0f) * 5.0f;
                SplineRotation.Roll += HeatBanking * GetCurrentTimeOfDayMultiplier();
            }
            break;

        case EAuracronRailType::Axis:
            // Maintain precise axis alignment
            SplineRotation.Roll = 0.0f; // No banking for axis rails
            break;

        case EAuracronRailType::Lunar:
            // Add ethereal swaying
            {
                float EtherealSway = FMath::Sin(Progress * 4.0f + LastUpdateTime) * 3.0f;
                SplineRotation.Roll += EtherealSway * CalculateNightIntensity();
            }
            break;

        default:
            break;
    }

    return SplineRotation;
}

void AAuracronDynamicRail::SetRailPath(const TArray<FVector>& PathPoints)
{
    if (!RailSpline || PathPoints.Num() < 2)
    {
        return;
    }

    // Set rail path using UE 5.6 spline path system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting rail path with %d points"), PathPoints.Num());

    // Clear existing spline points
    RailSpline->ClearSplinePoints();

    // Add new spline points
    for (int32 i = 0; i < PathPoints.Num(); i++)
    {
        RailSpline->AddSplinePoint(PathPoints[i], ESplineCoordinateSpace::World);
        RailSpline->SetSplinePointType(i, ESplinePointType::CurveClamped);
    }

    // Update spline tangents for smooth curves
    RailSpline->UpdateSpline();

    // Rebuild rail mesh and effects with new path
    BuildRailMesh();
    SetupRailEffects();
    CreateEntryExitPoints();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Rail path updated - Length: %.1f units"), RailSpline ? RailSpline->GetSplineLength() : 0.0f);
}

TArray<FVector> AAuracronDynamicRail::GetRailPath() const
{
    TArray<FVector> PathPoints;

    if (RailSpline)
    {
        int32 NumPoints = RailSpline->GetNumberOfSplinePoints();
        PathPoints.Reserve(NumPoints);

        for (int32 i = 0; i < NumPoints; i++)
        {
            PathPoints.Add(RailSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World));
        }
    }

    return PathPoints;
}

void AAuracronDynamicRail::ResetToDefaultConfiguration()
{
    TRACE_CPUPROFILER_EVENT_SCOPE(AAuracronDynamicRail::ResetToDefaultConfiguration);

    // Production Ready: Reset rail to default configuration
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Resetting rail to default configuration"));

    // Reset rail type to default
    RailType = EAuracronRailType::Solar;

    // Reset movement data to defaults
    MovementData.MovementSpeed = 1000.0f;
    MovementData.Acceleration = 500.0f;
    MovementData.Deceleration = 500.0f;
    MovementData.bCanReverseDirection = true;
    MovementData.bCanExitAnywhere = true;
    MovementData.EnergyCostPerSecond = 5.0f;

    // Reset visual configuration to defaults
    VisualConfig.RailMaterial = nullptr;
    VisualConfig.RailEffect = nullptr;
    VisualConfig.MovementTrailEffect = nullptr;
    VisualConfig.ActivationEffect = nullptr;
    VisualConfig.RailColor = FLinearColor::White;
    VisualConfig.EffectIntensity = 1.0f;
    VisualConfig.bVisibleDuringDay = true;
    VisualConfig.bVisibleDuringNight = true;

    // Reset activation state
    bIsActive = false;
    bAutoActivate = true;

    // Clear any active players
    for (APawn* Player : PlayersOnRail)
    {
        if (Player)
        {
            StopPlayerMovement(Player);
        }
    }
    PlayersOnRail.Empty();

    // Reset spline to default path if it exists
    if (RailSpline)
    {
        RailSpline->ClearSplinePoints();

        // Create a simple default path (straight line)
        FVector StartLocation = GetActorLocation();
        FVector EndLocation = StartLocation + GetActorForwardVector() * 1000.0f;

        RailSpline->AddSplinePoint(StartLocation, ESplineCoordinateSpace::World);
        RailSpline->AddSplinePoint(EndLocation, ESplineCoordinateSpace::World);
        RailSpline->UpdateSpline();
    }

    // Update visual components
    UpdateRailVisuals(0.0f);

    // Recreate entry/exit points
    CreateEntryExitPoints();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Rail reset to default configuration completed"));
}

FVector AAuracronDynamicRail::GetSplineLocationAtDistance(float Distance) const
{
    if (!RailSpline)
    {
        return GetActorLocation();
    }

    return RailSpline->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
}

FVector AAuracronDynamicRail::GetSplineDirectionAtDistance(float Distance) const
{
    if (!RailSpline)
    {
        return GetActorForwardVector();
    }

    return RailSpline->GetDirectionAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
}

FVector AAuracronDynamicRail::GetRailStartPosition() const
{
    return RailSpline ? RailSpline->GetLocationAtSplinePoint(0, ESplineCoordinateSpace::World) : GetActorLocation();
}

FVector AAuracronDynamicRail::GetRailEndPosition() const
{
    if (!RailSpline)
    {
        return GetActorLocation();
    }

    int32 LastPoint = RailSpline->GetNumberOfSplinePoints() - 1;
    return RailSpline->GetLocationAtSplinePoint(LastPoint, ESplineCoordinateSpace::World);
}

// === Final Implementation Methods ===

void AAuracronDynamicRail::PlayRailActivationEffects()
{
    // Play activation effects using UE 5.6 activation system
    if (VisualConfig.ActivationEffect)
    {
        UNiagaraComponent* ActivationComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            VisualConfig.ActivationEffect,
            RootComponent,
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true // Auto-destroy
        );

        if (ActivationComponent)
        {
            ActivationComponent->SetVariableLinearColor(FName("ActivationColor"), VisualConfig.RailColor);
            ActivationComponent->SetVariableFloat(FName("ActivationIntensity"), VisualConfig.EffectIntensity * 2.0f);
        }
    }

    // Play activation audio
    static const FSoftObjectPath ActivationAudioPath(TEXT("/Game/Audio/Rails/MS_RailActivation.MS_RailActivation"));
    if (UMetaSoundSource* ActivationAudio = Cast<UMetaSoundSource>(ActivationAudioPath.TryLoad()))
    {
        UAudioComponent* ActivationAudioComponent = UGameplayStatics::SpawnSoundAttached(
            ActivationAudio,
            RootComponent,
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true // Auto-destroy
        );

        if (ActivationAudioComponent)
        {
            ActivationAudioComponent->SetFloatParameter(TEXT("RailType"), static_cast<float>(RailType));
            ActivationAudioComponent->SetFloatParameter(TEXT("ActivationIntensity"), VisualConfig.EffectIntensity);
        }
    }
}

void AAuracronDynamicRail::PlayRailDeactivationEffects()
{
    // Play deactivation effects using UE 5.6 deactivation system
    static const FSoftObjectPath DeactivationVFXPath(TEXT("/Game/VFX/Rails/NS_RailDeactivation.NS_RailDeactivation"));
    if (UNiagaraSystem* DeactivationVFX = Cast<UNiagaraSystem>(DeactivationVFXPath.TryLoad()))
    {
        UNiagaraComponent* DeactivationComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            DeactivationVFX,
            RootComponent,
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true // Auto-destroy
        );

        if (DeactivationComponent)
        {
            DeactivationComponent->SetVariableLinearColor(FName("DeactivationColor"), VisualConfig.RailColor * 0.5f);
            DeactivationComponent->SetVariableFloat(FName("DeactivationIntensity"), VisualConfig.EffectIntensity);
        }
    }
}

void AAuracronDynamicRail::UpdateRailVisuals(float DeltaTime)
{
    if (!bIsActive)
    {
        return;
    }

    // Update rail visuals using UE 5.6 visual update system
    float TimeOfDayMultiplier = GetCurrentTimeOfDayMultiplier();

    // Update VFX parameters
    if (RailVFX)
    {
        RailVFX->SetVariableFloat(FName("TimeOfDayMultiplier"), TimeOfDayMultiplier);
        RailVFX->SetVariableFloat(FName("ActivePlayerCount"), static_cast<float>(ActivePlayerCount));
        RailVFX->SetVariableFloat(FName("RailUsageIntensity"), FMath::Min(ActivePlayerCount * 0.3f, 2.0f));
    }

    // Update dynamic materials
    for (UMaterialInstanceDynamic* DynamicMaterial : DynamicRailMaterials)
    {
        if (DynamicMaterial)
        {
            DynamicMaterial->SetScalarParameterValue(TEXT("TimeOfDay"), TimeOfDayMultiplier);
            DynamicMaterial->SetScalarParameterValue(TEXT("UsageIntensity"), FMath::Min(ActivePlayerCount * 0.2f, 1.5f));

            // Add flowing energy effect
            float FlowProgress = FMath::Fmod(LastUpdateTime * 0.5f, 1.0f);
            DynamicMaterial->SetScalarParameterValue(TEXT("EnergyFlow"), FlowProgress);
        }
    }

    // Update audio parameters
    if (RailAudio && RailAudio->IsPlaying())
    {
        RailAudio->SetFloatParameter(TEXT("TimeOfDay"), TimeOfDayMultiplier);
        RailAudio->SetFloatParameter(TEXT("PlayerActivity"), FMath::Min(ActivePlayerCount * 0.25f, 1.0f));
    }
}

void AAuracronDynamicRail::UpdateRailVisibility()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Update rail visibility based on time and type using UE 5.6 visibility system
    bool bShouldBeVisible = true;

    switch (RailType)
    {
        case EAuracronRailType::Solar:
            bShouldBeVisible = VisualConfig.bVisibleDuringDay ? true : IsNightTime();
            break;
        case EAuracronRailType::Axis:
            bShouldBeVisible = true; // Always visible
            break;
        case EAuracronRailType::Lunar:
            bShouldBeVisible = VisualConfig.bVisibleDuringNight ? IsNightTime() : false;
            break;
        default:
            break;
    }

    // Apply visibility changes
    SetActorHiddenInGame(!bShouldBeVisible);

    // Update VFX visibility
    if (RailVFX)
    {
        if (bShouldBeVisible && bIsActive)
        {
            if (!RailVFX->IsActive())
            {
                RailVFX->Activate();
            }
        }
        else
        {
            if (RailVFX->IsActive())
            {
                RailVFX->Deactivate();
            }
        }
    }

    LastVisibilityCheck = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
}

void AAuracronDynamicRail::OptimizeRailRendering(const FVector& ViewerLocation)
{
    if (!GetWorld())
    {
        return;
    }

    // Optimize rail rendering based on distance using UE 5.6 optimization system
    float DistanceToViewer = FVector::Dist(GetActorLocation(), ViewerLocation);

    // Calculate LOD level based on distance
    int32 LODLevel = 0;
    if (DistanceToViewer > 5000.0f)
    {
        LODLevel = 3; // Lowest detail
    }
    else if (DistanceToViewer > 2000.0f)
    {
        LODLevel = 2; // Medium detail
    }
    else if (DistanceToViewer > 1000.0f)
    {
        LODLevel = 1; // High detail
    }
    else
    {
        LODLevel = 0; // Maximum detail
    }

    // Apply LOD to mesh component
    if (RailMesh)
    {
        RailMesh->SetForcedLodModel(LODLevel + 1); // UE uses 1-based LOD indexing
    }

    // Optimize VFX based on distance
    if (RailVFX)
    {
        float VFXQuality = FMath::Clamp(1.0f - (DistanceToViewer / 10000.0f), 0.2f, 1.0f);
        RailVFX->SetVariableFloat(FName("QualityLevel"), VFXQuality);
        RailVFX->SetVariableFloat(FName("ParticleCount"), VFXQuality * 100.0f);
    }

    // Optimize audio based on distance
    if (RailAudio)
    {
        float AudioVolume = FMath::Clamp(1.0f - (DistanceToViewer / 3000.0f), 0.0f, 1.0f);
        RailAudio->SetVolumeMultiplier(AudioVolume * 0.7f);
    }
}

// === Getter Methods Implementation ===

EAuracronRailType AAuracronDynamicRail::GetRailType() const
{
    return RailType;
}

float AAuracronDynamicRail::GetRailLength() const
{
    return RailSpline ? RailSpline->GetSplineLength() : 0.0f;
}

bool AAuracronDynamicRail::IsPlayerOnRail(APawn* Player) const
{
    return Player && PlayersOnRail.Contains(Player);
}

FRailMovementData AAuracronDynamicRail::GetMovementData() const
{
    return MovementData;
}

FRailVisualConfig AAuracronDynamicRail::GetVisualConfig() const
{
    return VisualConfig;
}

void AAuracronDynamicRail::SetMovementData(const FRailMovementData& NewMovementData)
{
    MovementData = NewMovementData;
    // Apply movement data changes
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Rail movement data updated"));
}

void AAuracronDynamicRail::SetVisualConfig(const FRailVisualConfig& NewVisualConfig)
{
    VisualConfig = NewVisualConfig;
    // Apply visual configuration changes
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Rail visual config updated"));
}

// === Player Access Control Implementation ===

void AAuracronDynamicRail::GrantPlayerAccess(APawn* Player)
{
    if (!Player)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot grant access to null player"));
        return;
    }

    // Add player to authorized list using UE 5.6 access control system
    AuthorizedPlayers.Add(Player);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Granted rail access to player %s for %s rail"),
        *Player->GetName(), *UEnum::GetValueAsString(RailType));

    // Apply visual indicator that player has access
    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        static const FSoftClassPath AccessGrantedEffectPath(TEXT("/Game/GameplayEffects/Rails/GE_RailAccessGranted.GE_RailAccessGranted_C"));
        if (TSubclassOf<UGameplayEffect> AccessEffectClass = AccessGrantedEffectPath.TryLoadClass<UGameplayEffect>())
        {
            const UGameplayEffect* AccessEffect = AccessEffectClass.GetDefaultObject();
            if (AccessEffect)
            {
                FGameplayEffectContextHandle EffectContext = PlayerASC->MakeEffectContext();
                EffectContext.AddSourceObject(this);

                FGameplayEffectSpec AccessSpec(AccessEffect, EffectContext, 1.0f);
                AccessSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Rail.AccessLevel")), 1.0f);
                AccessSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Rail.Type")), static_cast<float>(RailType));
                AccessSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

                PlayerASC->ApplyGameplayEffectSpecToSelf(AccessSpec);
            }
        }
    }

    // Spawn access granted VFX
    static const FSoftObjectPath AccessVFXPath(TEXT("/Game/VFX/Rails/NS_AccessGranted.NS_AccessGranted"));
    if (UNiagaraSystem* AccessVFX = Cast<UNiagaraSystem>(AccessVFXPath.TryLoad()))
    {
        UNiagaraComponent* AccessComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            AccessVFX,
            Player->GetRootComponent(),
            NAME_None,
            FVector(0.0f, 0.0f, 100.0f),
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true // Auto-destroy
        );

        if (AccessComponent)
        {
            AccessComponent->SetVariableLinearColor(FName("AccessColor"), VisualConfig.RailColor);
            AccessComponent->SetVariableFloat(FName("AccessIntensity"), 1.0f);
        }
    }
}

void AAuracronDynamicRail::RevokePlayerAccess(APawn* Player)
{
    if (!Player)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot revoke access from null player"));
        return;
    }

    // Remove player from authorized list
    bool bWasAuthorized = AuthorizedPlayers.Remove(Player) > 0;

    if (bWasAuthorized)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Revoked rail access from player %s for %s rail"),
            *Player->GetName(), *UEnum::GetValueAsString(RailType));

        // If player is currently on this rail, stop their movement
        if (IsPlayerOnRail(Player))
        {
            StopPlayerMovement(Player);
        }

        // Remove access-related gameplay effects
        if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
        {
            // Remove all rail access effects
            FGameplayTagContainer AccessTags;
            AccessTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Effect.Rail.Access")));

            TArray<FActiveGameplayEffectHandle> EffectsToRemove = PlayerASC->GetActiveEffectsWithAllTags(AccessTags);

            for (const FActiveGameplayEffectHandle& EffectHandle : EffectsToRemove)
            {
                PlayerASC->RemoveActiveGameplayEffect(EffectHandle);
            }
        }

        // Spawn access revoked VFX
        static const FSoftObjectPath RevokeVFXPath(TEXT("/Game/VFX/Rails/NS_AccessRevoked.NS_AccessRevoked"));
        if (UNiagaraSystem* RevokeVFX = Cast<UNiagaraSystem>(RevokeVFXPath.TryLoad()))
        {
            UNiagaraComponent* RevokeComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                RevokeVFX,
                Player->GetRootComponent(),
                NAME_None,
                FVector(0.0f, 0.0f, 100.0f),
                FRotator::ZeroRotator,
                EAttachLocation::KeepRelativeOffset,
                true // Auto-destroy
            );

            if (RevokeComponent)
            {
                RevokeComponent->SetVariableLinearColor(FName("RevokeColor"), FLinearColor::Red);
                RevokeComponent->SetVariableFloat(FName("RevokeIntensity"), 0.8f);
            }
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Player %s was not authorized for %s rail"),
            *Player->GetName(), *UEnum::GetValueAsString(RailType));
    }
}

bool AAuracronDynamicRail::HasPlayerAccess(APawn* Player) const
{
    if (!Player)
    {
        return false;
    }

    // Check if player is in authorized list
    bool bHasAccess = AuthorizedPlayers.Contains(Player);

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Player %s access check for %s rail: %s"),
        *Player->GetName(), *UEnum::GetValueAsString(RailType), bHasAccess ? TEXT("GRANTED") : TEXT("DENIED"));

    return bHasAccess;
}

// ========================================
// Missing Function Implementations
// ========================================

int32 AAuracronDynamicRail::GetActivePlayerCount() const
{
    return PlayersOnRail.Num();
}

void AAuracronDynamicRail::SetPlayerRailSpeed(APawn* Player, float Speed)
{
    if (!Player || !IsPlayerOnRail(Player))
    {
        return;
    }

    FPlayerRailData* RailData = PlayerRailDataMap.Find(Player);
    if (RailData)
    {
        float MaxAllowedSpeed = MovementData.MaxSpeed * 2.0f;
        RailData->CurrentSpeed = FMath::Clamp(Speed, 0.0f, MaxAllowedSpeed);
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Set rail speed for player %s to %.1f"), *Player->GetName(), RailData->CurrentSpeed);
    }
}

float AAuracronDynamicRail::GetPlayerRailProgress(APawn* Player) const
{
    if (!Player || !IsPlayerOnRail(Player))
    {
        return 0.0f;
    }

    const FPlayerRailData* RailData = PlayerRailDataMap.Find(Player);
    return RailData ? RailData->Progress : 0.0f;
}

FVector AAuracronDynamicRail::GetPlayerRailPosition(APawn* Player) const
{
    if (!Player || !IsPlayerOnRail(Player))
    {
        return FVector::ZeroVector;
    }

    const FPlayerRailData* RailData = PlayerRailDataMap.Find(Player);
    if (RailData)
    {
        return CalculateRailPosition(RailData->Progress);
    }

    return Player->GetActorLocation();
}

void AAuracronDynamicRail::OnEntryPointBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    APawn* Player = Cast<APawn>(OtherActor);
    if (!Player || !CanPlayerUseRail(Player))
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s entered rail entry point"), *Player->GetName());

    // Show interaction prompt
    ShowRailInteractionPrompt(Player);

    // Add to potential users
    PotentialUsers.AddUnique(Player);

    // Trigger entry point event
    OnPlayerEnteredRailArea.Broadcast(Player);
}

void AAuracronDynamicRail::OnExitPointBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    APawn* Player = Cast<APawn>(OtherActor);
    if (!Player)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s reached rail exit point"), *Player->GetName());

    // Stop player movement if they're on the rail
    if (IsPlayerOnRail(Player))
    {
        StopPlayerMovement(Player);
    }

    // Remove from potential users
    PotentialUsers.Remove(Player);

    // Hide interaction prompt
    HideRailInteractionPrompt(Player);

    // Trigger exit point event
    OnPlayerReachedDestination.Broadcast(Player);
}

FVector AAuracronDynamicRail::GetRailStartLocation() const
{
    return GetRailStartPosition();
}

FVector AAuracronDynamicRail::GetRailEndLocation() const
{
    return GetRailEndPosition();
}

float AAuracronDynamicRail::GetDistanceToRail(const FVector& Location) const
{
    if (!RailSpline)
    {
        return FVector::Dist(Location, GetActorLocation());
    }

    // Find closest point on spline to the given location
    FVector ClosestLocation = RailSpline->FindLocationClosestToWorldLocation(Location, ESplineCoordinateSpace::World);
    return FVector::Dist(Location, ClosestLocation);
}
